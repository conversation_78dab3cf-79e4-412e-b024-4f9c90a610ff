/**
 * User Data Management
 * Imports user data from JSON file and provides utility functions
 */

import { User } from "@/types/auth";
import usersData from "./users.json";

export const MOCK_USERS: User[] = usersData.users;

export const getActiveUsers = (): User[] => {
  return MOCK_USERS.filter((user) => user.status === "active");
};

export const findUserByUsername = (username: string): User | undefined => {
  return MOCK_USERS.find(
    (user) => user.username === username && user.status === "active"
  );
};

export const findUserById = (id: string): User | undefined => {
  return MOCK_USERS.find((user) => user.id === id && user.status === "active");
};

export const validateUserCredentials = (
  username: string,
  password: string
): User | undefined => {
  return MOCK_USERS.find(
    (user) =>
      user.username === username &&
      user.password === password &&
      user.status === "active"
  );
};
