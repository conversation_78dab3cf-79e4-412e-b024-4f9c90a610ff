import ClockCard from '@/components/shared/ClockComponent'
import React from 'react'

export default function ClockPage() {
  return (
   // This page is used to test different configurations of the ClockCard component

      // Basic ClockCard usage with default settings.
        <ClockCard />
     
        // ClockCard with custom configuration:
        // - variant="compact": renders a smaller, compact card.
        // - showSettings={false}: hides the settings UI.
        // - className="custom-class": adds custom styling via CSS class.
        // <ClockCard
        // variant="compact"
        // showSettings={false}
        // className="custom-class"
        // />

        //  ClockCard with fine-grained UI controls:
        // - enableTimezoneControl: toggles timezone selector.
        // - enableDisplayStyleControl: toggles between analog/digital views.
        // - enableTimeFormatControl: allows switching between 12h/24h format.
        // - enableDateFormatControl: allows changing date format display.
        // <ClockCard
        // variant="default"
        // enableTimezoneControl={false}
        // enableDisplayStyleControl={false}
        // enableTimeFormatControl={true}
        // enableDateFormatControl={true}
        // />




  )
}
