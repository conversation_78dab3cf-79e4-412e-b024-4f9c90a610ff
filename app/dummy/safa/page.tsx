"use client";

import React, { useState } from "react";
import SummaryCard from "../../../components/SummaryCard";
import { useLanguage } from "../../../contexts/LanguageContext";
import DashboardSummaryCards from "../../../components/DashboardSummaryCards";

export default function DummyCardsPage() {
  const { t, dir } = useLanguage();
  const [cardSize, setCardSize] = useState<"sm" | "md" | "lg">("md");
  const [showTrends, setShowTrends] = useState(true);
  const [layout, setLayout] = useState<
    "horizontal" | "grid-2x3" | "grid-3x2" | "auto"
  >("auto");

  // Dummy data matching the reference screenshot
  const dummyCards = [
    {
      id: "inactive-trips-count",
      titleKey: "dashboard.card.inactiveTrips",
      value: 1551,
      icon: "bell-off" as const,
      color: "gray" as const,
      trend: { value: 0, direction: "stable" as const },
    },
    {
      id: "resolved-trips-count",
      titleKey: "dashboard.card.resolvedToday",
      value: 1562,
      icon: "check" as const,
      color: "green" as const,
      trend: { value: 8.7, direction: "up" as const },
    },
    {
      id: "critical-alerts-count",
      titleKey: "dashboard.card.criticalAlerts",
      value: 0,
      icon: "prohibition" as const,
      color: "red" as const,
      trend: { value: 12.3, direction: "down" as const },
    },
    {
      id: "pending-reviews-count",
      titleKey: "dashboard.card.pendingAlerts",
      value: 847,
      icon: "bell-gray" as const,
      color: "gray" as const,
      trend: { value: 3.4, direction: "up" as const },
    },
    {
      id: "completed-today-count",
      titleKey: "dashboard.card.totalAlerts",
      value: 2156,
      icon: "check" as const,
      color: "blue" as const,
      trend: { value: 15.2, direction: "up" as const },
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50" dir={dir}>
      <div className="p-6">
        {/* Header Section */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            {/* Controls */}
            <div
              className={`flex items-center ${
                dir === "rtl" ? "space-x-reverse space-x-4" : "space-x-4"
              }`}
            >
              <div
                className={`flex items-center ${
                  dir === "rtl" ? "space-x-reverse space-x-2" : "space-x-2"
                }`}
              >
                <label className="text-sm font-medium text-gray-700">
                  {t("dummy.controls.layout")}
                </label>
                <select
                  value={layout}
                  onChange={(e) =>
                    setLayout(
                      e.target.value as
                        | "horizontal"
                        | "grid-2x3"
                        | "grid-3x2"
                        | "auto"
                    )
                  }
                  className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="auto">{t("dummy.layout.autoGrid")}</option>
                  <option value="horizontal">
                    {t("dummy.layout.horizontal")}
                  </option>
                  <option value="grid-2x3">{t("dummy.layout.grid2x3")}</option>
                  <option value="grid-3x2">{t("dummy.layout.grid3x2")}</option>
                </select>
              </div>

              <div
                className={`flex items-center ${
                  dir === "rtl" ? "space-x-reverse space-x-2" : "space-x-2"
                }`}
              >
                <label className="text-sm font-medium text-gray-700">
                  {t("dummy.controls.size")}
                </label>
                <select
                  value={cardSize}
                  onChange={(e) =>
                    setCardSize(e.target.value as "sm" | "md" | "lg")
                  }
                  className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="sm">{t("dummy.size.small")}</option>
                  <option value="md">{t("dummy.size.medium")}</option>
                  <option value="lg">{t("dummy.size.large")}</option>
                </select>
              </div>

              <div
                className={`flex items-center ${
                  dir === "rtl" ? "space-x-reverse space-x-2" : "space-x-2"
                }`}
              >
                <label className="text-sm font-medium text-gray-700">
                  {t("dummy.controls.trends")}
                </label>
                <button
                  onClick={() => setShowTrends(!showTrends)}
                  className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                    showTrends
                      ? "bg-blue-100 text-blue-700 border border-blue-200"
                      : "bg-gray-100 text-gray-700 border border-gray-200"
                  }`}
                >
                  {showTrends
                    ? t("dummy.controls.on")
                    : t("dummy.controls.off")}
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Dashboard Summary Cards Section */}
        <div className="mb-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            {t("dummy.section.dashboardCards")}
          </h2>
          <div className="bg-white rounded-lg shadow-sm p-4">
            <DashboardSummaryCards
              cards={dummyCards}
              layout={layout}
              showTrends={showTrends}
              cardSize={cardSize}
              className="mb-2"
            />
          </div>
        </div>

        Individual Cards Section for Comparison
        <div className="mb-4">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            {t("dummy.section.individualCards")}
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4">
            {dummyCards.slice(0, 5).map((card) => (
              <SummaryCard
                key={`individual-${card.id}`}
                id={card.id}
                titleKey={card.titleKey}
                value={card.value}
                icon={card.icon}
                color={card.color}
                size={cardSize}
                showTrends={showTrends}
                trend={card.trend}
                className="w-full"
              />
            ))}
          </div>
        </div>

        {/* Data Table Section - Matching Reference */}
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">
              {t("dummy.table.title")}
            </h2>
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Number
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Shipment Description
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Entry and Exit point
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Net Sales
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Trader
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Driver Details
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Result
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Optional
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Trip Reference
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {/* Sample rows matching the reference */}
                {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((index) => (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div
                          className={`w-3 h-3 rounded-full ${
                            index % 3 === 0
                              ? "bg-yellow-400"
                              : index % 3 === 1
                              ? "bg-green-400"
                              : "bg-gray-400"
                          }`}
                        ></div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {index}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      ShipmentDescription...
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      King Fahad Int Airport - Riyadh Border
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {(Math.random() * 10000).toFixed(2)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      Trader in Entry Border
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      ABC-123456789, Musaab
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <span className="text-green-600">✓</span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div className="flex space-x-1">
                        <span className="text-red-500">🔔</span>
                        <span className="text-red-500">⚠</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-blue-600">
                      Trip Reference
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
}
