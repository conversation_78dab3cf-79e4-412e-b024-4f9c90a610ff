"use client";

import React, { useEffect, useState } from "react";

export default function TestMapPage() {
  const [apiKey, setApiKey] = useState<string>("");
  const [testResult, setTestResult] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const key = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;
    setApiKey(key || "Not found");
  }, []);

  const testApiKey = async () => {
    setIsLoading(true);
    setTestResult("Testing...");

    try {
      // Test the API key by making a simple request
      const response = await fetch(
        `https://maps.googleapis.com/maps/api/js?key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}&libraries=geometry`
      );
      
      if (response.ok) {
        setTestResult("✅ API key appears to be valid");
      } else {
        setTestResult(`❌ API key test failed: ${response.status} ${response.statusText}`);
      }
    } catch (error) {
      setTestResult(`❌ Error testing API key: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testDirectLoad = () => {
    setIsLoading(true);
    setTestResult("Loading Google Maps script...");

    const script = document.createElement("script");
    script.src = `https://maps.googleapis.com/maps/api/js?key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}&libraries=geometry`;
    script.async = true;
    script.defer = true;

    script.onload = () => {
      setTestResult("✅ Google Maps script loaded successfully");
      setIsLoading(false);
    };

    script.onerror = (error) => {
      setTestResult(`❌ Failed to load Google Maps script: ${error}`);
      setIsLoading(false);
    };

    document.head.appendChild(script);
  };

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          Google Maps API Test
        </h1>

        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">API Key Information</h2>
          <div className="space-y-2">
            <p>
              <strong>API Key:</strong>{" "}
              <code className="bg-gray-100 px-2 py-1 rounded">
                {apiKey.length > 20 ? `${apiKey.substring(0, 20)}...` : apiKey}
              </code>
            </p>
            <p>
              <strong>Length:</strong> {apiKey.length} characters
            </p>
            <p>
              <strong>Status:</strong>{" "}
              <span className={apiKey === "Not found" ? "text-red-600" : "text-green-600"}>
                {apiKey === "Not found" ? "❌ Not configured" : "✅ Found"}
              </span>
            </p>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">API Tests</h2>
          <div className="space-y-4">
            <div>
              <button
                onClick={testApiKey}
                disabled={isLoading || apiKey === "Not found"}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed mr-4"
              >
                {isLoading ? "Testing..." : "Test API Key"}
              </button>
              
              <button
                onClick={testDirectLoad}
                disabled={isLoading || apiKey === "Not found"}
                className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
              >
                {isLoading ? "Loading..." : "Test Direct Script Load"}
              </button>
            </div>
            
            {testResult && (
              <div className="mt-4 p-4 bg-gray-50 rounded border">
                <strong>Test Result:</strong>
                <div className="mt-2">{testResult}</div>
              </div>
            )}
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Troubleshooting</h2>
          <div className="space-y-2 text-sm text-gray-600">
            <p>• Make sure your API key is valid and not expired</p>
            <p>• Ensure the API key has the following APIs enabled:</p>
            <ul className="ml-4 list-disc">
              <li>Maps JavaScript API</li>
              <li>Geocoding API (optional)</li>
              <li>Places API (optional)</li>
            </ul>
            <p>• Check if there are any domain restrictions on your API key</p>
            <p>• Verify your billing account is active (Google Maps requires billing)</p>
          </div>
        </div>
      </div>
    </div>
  );
}
