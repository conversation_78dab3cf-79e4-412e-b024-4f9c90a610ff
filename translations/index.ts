import { en } from "./en";
import { ar } from "./ar";

export type Language = "en" | "ar";

export type TranslationKey = keyof typeof en;

export const translations = {
  en,
  ar,
} as const;

export { en, ar };


/**
 * Get translation for a specific key
 */
export const getTranslation = (language: Language, key: string): string => {
  // const keys = key.split(".");
  let value: any = translations[language];

  // for (const k of keys) {
  //   value = value?.[k];
  // }
  value = value?.[key];
  return value || key;
};

/**
 * Get direction for language (RTL for Arabic, LTR for English)
 */
export const getDirection = (language: Language): "rtl" | "ltr" => {
  return language === "ar" ? "rtl" : "ltr";
};

/**
 * Get text alignment for language
 */
export const getTextAlign = (language: Language): "right" | "left" => {
  return language === "ar" ? "right" : "left";
};

/**
 * Get flex direction for language (reverse for Arabic)
 */
export const getFlexDirection = (language: Language): "row" | "row-reverse" => {
  return language === "ar" ? "row-reverse" : "row";
};
