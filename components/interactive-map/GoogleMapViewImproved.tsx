/**
 * @fileoverview Improved Google Map View Component
 * @description Enhanced Google Maps component using @googlemaps/js-api-loader for better reliability
 */

"use client";

import React, { useRef, useEffect, useState, useCallback } from "react";
import { Loader } from "@googlemaps/js-api-loader";
import { Location, MapCenter } from "../../types/map";
import MapLoadingScreen from "./MapLoadingScreen";

interface GoogleMapViewImprovedProps {
  locations: Location[];
  center: MapCenter;
  zoom: number;
  language?: "en" | "ar";
  onLocationClick?: (location: Location) => void;
}

const GoogleMapViewImproved: React.FC<GoogleMapViewImprovedProps> = ({
  locations,
  center,
  zoom,
  language = "en",
  onLocationClick,
}) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const [map, setMap] = useState<google.maps.Map | null>(null);
  const [markers, setMarkers] = useState<google.maps.Marker[]>([]);
  const [isLoaded, setIsLoaded] = useState(false);
  const [loadingError, setLoadingError] = useState<string | null>(null);
  const [loadingStartTime] = useState<number>(Date.now());
  const [loadingTime, setLoadingTime] = useState<number>(0);
  const [activeInfoWindow, setActiveInfoWindow] = useState<google.maps.InfoWindow | null>(null);
  const [currentLocation, setCurrentLocation] = useState<Location | null>(null);

  // Update loading time every second
  useEffect(() => {
    if (isLoaded) return;
    
    const interval = setInterval(() => {
      setLoadingTime(Math.floor((Date.now() - loadingStartTime) / 1000));
    }, 1000);

    return () => clearInterval(interval);
  }, [isLoaded, loadingStartTime]);

  // Load Google Maps API using the official loader
  useEffect(() => {
    const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;
    
    if (!apiKey) {
      console.error("Google Maps API key is not defined");
      setLoadingError("Google Maps API key is not configured. Please check your environment variables.");
      return;
    }

    console.log("Loading Google Maps API with key:", `${apiKey.substring(0, 10)}...`);

    const loader = new Loader({
      apiKey: apiKey,
      version: "weekly",
      libraries: ["geometry", "places"],
      language: language,
      region: language === "ar" ? "SA" : "US",
    });

    loader
      .load()
      .then(() => {
        console.log("Google Maps API loaded successfully");
        setIsLoaded(true);
      })
      .catch((error) => {
        console.error("Failed to load Google Maps API:", error);
        setLoadingError(`Failed to load Google Maps API: ${error.message || 'Unknown error'}`);
      });
  }, [language]);

  // Initialize map
  useEffect(() => {
    if (!isLoaded || !mapRef.current) {
      console.log("Map initialization skipped:", { isLoaded, hasMapRef: !!mapRef.current });
      return;
    }

    try {
      console.log("Initializing Google Map with center:", center, "zoom:", zoom);
      const mapInstance = new google.maps.Map(mapRef.current, {
        center,
        zoom,
        mapTypeId: google.maps.MapTypeId.ROADMAP,
        mapTypeControl: true,
        streetViewControl: true,
        fullscreenControl: true,
        zoomControl: true,
      });

      console.log("Google Map initialized successfully");
      setMap(mapInstance);
    } catch (error) {
      console.error("Error initializing Google Map:", error);
      setLoadingError(`Failed to initialize Google Map: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }, [isLoaded, center, zoom]);

  // Generate tooltip content
  const generateTooltipContent = useCallback(
    (location: Location) => {
      const getLocationTypeLabel = (type: string) => {
        const typeLabels = {
          airport: language === "ar" ? "مطار" : "Airport",
          seaport: language === "ar" ? "ميناء بحري" : "Seaport",
          landport: language === "ar" ? "ميناء بري" : "Land Port",
          police_station: language === "ar" ? "مركز شرطة" : "Police Station",
          checkpoint: language === "ar" ? "نقطة تفتيش" : "Checkpoint",
        };
        return typeLabels[type as keyof typeof typeLabels] || type.replace("_", " ");
      };

      return `
      <div style="padding: 12px; min-width: 220px; font-family: system-ui, -apple-system, sans-serif;">
        <h3 style="font-weight: 600; font-size: 16px; margin-bottom: 8px; color: #1f2937; line-height: 1.2;">
          ${language === "ar" ? location.nameAr : location.name}
        </h3>
        <p style="font-size: 13px; color: #6b7280; margin-bottom: 6px; line-height: 1.4;">
          📍 ${language === "ar" ? location.descriptionAr || location.description : location.description || ""}
        </p>
        <div style="display: flex; align-items: center; margin-bottom: 6px;">
          <span style="font-size: 12px; color: #2563eb; font-weight: 500; background: #eff6ff; padding: 2px 8px; border-radius: 12px;">
            ${getLocationTypeLabel(location.type)}
          </span>
        </div>
        <p style="font-size: 12px; color: #059669; margin-top: 8px; font-weight: 500;">
          ${language === "ar" ? "الحالة:" : "Status:"}
          <span style="color: ${location.status === "active" ? "#059669" : "#dc2626"};">
            ${location.status === "active" ? (language === "ar" ? "نشط" : "Active") : (language === "ar" ? "غير نشط" : "Inactive")}
          </span>
        </p>
      </div>
    `;
    },
    [language]
  );

  // Create marker icon
  const createMarkerIcon = useCallback((type: string) => {
    const iconConfigs = {
      airport: { color: "#1e40af", shadowColor: "#1e3a8a" },
      seaport: { color: "#0369a1", shadowColor: "#075985" },
      landport: { color: "#059669", shadowColor: "#047857" },
      police_station: { color: "#dc2626", shadowColor: "#b91c1c" },
      checkpoint: { color: "#d97706", shadowColor: "#b45309" },
    };

    const config = iconConfigs[type as keyof typeof iconConfigs] || iconConfigs.checkpoint;

    return {
      url: `data:image/svg+xml;charset=UTF-8,${encodeURIComponent(`
        <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
          <circle cx="16" cy="16" r="12" fill="${config.color}" stroke="white" stroke-width="2"/>
          <circle cx="16" cy="16" r="8" fill="white" opacity="0.3"/>
        </svg>
      `)}`,
      scaledSize: new google.maps.Size(32, 32),
      anchor: new google.maps.Point(16, 16),
    };
  }, []);

  // Create and manage markers
  useEffect(() => {
    if (!map || !locations.length) return;

    // Clear existing markers
    markers.forEach((marker) => marker.setMap(null));

    // Create new markers
    const newMarkers = locations.map((location) => {
      const marker = new google.maps.Marker({
        position: { lat: location.lat, lng: location.lng },
        map: map,
        title: language === "ar" ? location.nameAr : location.name,
        icon: createMarkerIcon(location.type),
      });

      // Add info window
      const infoWindow = new google.maps.InfoWindow({
        content: generateTooltipContent(location),
      });

      marker.addListener("click", () => {
        if (activeInfoWindow) {
          activeInfoWindow.close();
        }

        infoWindow.open(map, marker);
        setActiveInfoWindow(infoWindow);
        setCurrentLocation(location);

        if (onLocationClick) {
          onLocationClick(location);
        }
      });

      return marker;
    });

    setMarkers(newMarkers);

    // Add click listener to map to close info windows
    const mapClickListener = map.addListener("click", () => {
      if (activeInfoWindow) {
        activeInfoWindow.close();
        setActiveInfoWindow(null);
        setCurrentLocation(null);
      }
    });

    return () => {
      google.maps.event.removeListener(mapClickListener);
    };
  }, [map, locations, language, onLocationClick, createMarkerIcon, generateTooltipContent, activeInfoWindow]);

  // Update active tooltip content when language changes
  useEffect(() => {
    if (activeInfoWindow && currentLocation) {
      activeInfoWindow.setContent(generateTooltipContent(currentLocation));
    }
  }, [language, activeInfoWindow, currentLocation, generateTooltipContent]);

  if (!isLoaded) {
    return (
      <MapLoadingScreen
        language={language}
        error={loadingError || undefined}
        loadingTime={loadingTime}
      />
    );
  }

  return <div ref={mapRef} className="w-full h-full" />;
};

export default GoogleMapViewImproved;
