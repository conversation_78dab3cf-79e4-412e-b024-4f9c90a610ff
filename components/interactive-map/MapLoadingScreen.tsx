/**
 * @fileoverview Map Loading Screen Component
 * @description Loading screen component for map with animated spinner and localized messages
 *
 * @features
 * - Animated loading spinner with smooth rotation
 * - Localized loading messages in Arabic and English
 * - Responsive centered layout
 * - Smooth fade-in animation
 * - Accessible loading indicator
 * - Error state display
 */

"use client";

import React, { useState, useEffect } from "react";

interface MapLoadingScreenProps {
  message?: string;
  messageAr?: string;
  language?: "en" | "ar";
  error?: string;
  loadingTime?: number;
}

const MapLoadingScreen: React.FC<MapLoadingScreenProps> = ({
  message = "Loading Map",
  messageAr = "جاري تحميل الخريطة",
  language = "en",
  error,
  loadingTime = 0,
}) => {
  const displayMessage = language === "ar" ? messageAr : message;
  const loadingText =
    language === "ar" ? "جاري تحميل خرائط جوجل..." : "Loading Google Maps...";
  const errorText =
    language === "ar" ? "حدث خطأ أثناء تحميل الخريطة" : "Error loading map";
  const retryText = language === "ar" ? "إعادة المحاولة" : "Retry";

  const [showLongLoadingMessage, setShowLongLoadingMessage] = useState(false);

  // Show additional message if loading takes too long
  useEffect(() => {
    const timer = setTimeout(() => {
      setShowLongLoadingMessage(true);
    }, 10000); // 10 seconds

    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="relative w-full h-full overflow-hidden">
      <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
        <div className="bg-white rounded-lg border p-8 text-center shadow-lg max-w-md">
          {error ? (
            <>
              <div className="text-red-500 text-5xl mb-4">⚠️</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {errorText}
              </h3>
              <p className="text-gray-500 text-sm mb-4">{error}</p>
              <button
                onClick={() => window.location.reload()}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
              >
                {retryText}
              </button>
            </>
          ) : (
            <>
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {displayMessage}
              </h3>
              <p className="text-gray-500 text-sm">{loadingText}</p>
              {showLongLoadingMessage && (
                <p className="text-amber-600 text-sm mt-4">
                  {language === "ar"
                    ? "يستغرق التحميل وقتًا أطول من المعتاد. يرجى التحقق من اتصالك بالإنترنت."
                    : "Loading is taking longer than usual. Please check your internet connection."}
                </p>
              )}
              <div className="mt-4 flex justify-center space-x-1">
                <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce"></div>
                <div
                  className="w-2 h-2 bg-blue-600 rounded-full animate-bounce"
                  style={{ animationDelay: "0.1s" }}
                ></div>
                <div
                  className="w-2 h-2 bg-blue-600 rounded-full animate-bounce"
                  style={{ animationDelay: "0.2s" }}
                ></div>
              </div>
              <p className="text-xs text-gray-400 mt-4">
                {language === "ar"
                  ? `وقت التحميل: ${loadingTime} ثانية`
                  : `Loading time: ${loadingTime} seconds`}
              </p>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default MapLoadingScreen;
