/**
 * @fileoverview Interactive Map Container Component
 * @description Main interactive map component that combines all sub-components
 * and manages data and global state for the map
 *
 * @features
 * - Interactive Google Maps display
 * - Sidebar with multiple tabs (search, alerts, routes)
 * - Support for different data types based on page type
 * - Automatic language detection from site settings
 * - Map control buttons (reset, fullscreen)
 * - Charts for displaying statistics
 *
 * @usage
 * ```tsx
 * // Basic usage
 * <InteractiveMapContainer />
 *
 * // With specific data type
 * <InteractiveMapContainer pageType="location-monitor" />
 *
 * // With custom data
 * <InteractiveMapContainer
 *   locations={customLocations}
 *   alerts={customAlerts}
 * />
 * ```
 */

"use client";

import React, { useMemo } from "react";
import { InteractiveMapProps } from "../../types/map";
import { usePageData } from "../../hooks/usePageData";
import { useSidebarState } from "../../hooks/useSidebarState";
import { useLanguage } from "../../contexts/LanguageContext";
import GoogleMapViewImproved from "./GoogleMapViewImproved";
import MapControlButtons from "./MapControlButtons";
import MapSidebar from "./MapSidebar";
import MapLoadingScreen from "./MapLoadingScreen";
import NoSSR from "../common/NoSSR";

/**
 * Interactive Map Container Component
 *
 * @description Main interactive map component that manages all sub-components
 * and handles data loading and state management
 *
 * @param {InteractiveMapProps} props - Component properties
 * @param {Location[]} [props.locations] - Custom locations array (optional)
 * @param {Alert[]} [props.alerts] - Custom alerts array (optional)
 * @param {Route[]} [props.routes] - Custom routes array (optional)
 * @param {TripStatistics} [props.statistics] - Custom statistics (optional)
 * @param {PageType} [props.pageType='default'] - Type of data to load
 * @param {MapCenter} [props.initialCenter] - Initial map center
 * @param {number} [props.initialZoom=6] - Initial zoom level
 * @param {boolean} [props.showSidebar=true] - Show sidebar
 * @param {'en'|'ar'} [props.language] - Interface language (auto-detected if not specified)
 * @param {Function} [props.onLocationClick] - Function called when clicking on location
 * @param {Function} [props.onAlertClick] - Function called when clicking on alert
 * @param {Function} [props.onRouteSelect] - Function called when selecting routes
 *
 * @returns {JSX.Element} Interactive map component
 *
 * @example
 * ```tsx
 * <InteractiveMapContainer
 *   pageType="location-monitor"
 *   initialCenter={{ lat: 24.7136, lng: 46.6753 }}
 *   onLocationClick={(location) => console.log(location)}
 * />
 * ```
 */
const InteractiveMapContainer: React.FC<InteractiveMapProps> = ({
  locations: externalLocations,
  alerts: externalAlerts,
  routes: externalRoutes,
  statistics: externalStatistics,
  pageType = "default",
  initialCenter = { lat: 24.7136, lng: 46.6753 },
  initialZoom = 6,
  showSidebar = true,
  language: externalLanguage,
  onLocationClick,
  onAlertClick,
  onRouteSelect,
}) => {
  // Auto-detect language from site settings
  const { language: detectedLanguage, isHydrated } = useLanguage();
  const language = externalLanguage || detectedLanguage;

  // Load data from JSON files based on page type or use external data
  const {
    locations: dataLocations,
    alerts: dataAlerts,
    routes: dataRoutes,
    statistics: dataStatistics,
    loading,
    error,
  } = usePageData(pageType);

  // Use external data if provided, otherwise use loaded data
  const mapData = useMemo(
    () => ({
      locations: externalLocations || dataLocations,
      alerts: externalAlerts || dataAlerts,
      routes: externalRoutes || dataRoutes,
      statistics: externalStatistics || dataStatistics,
    }),
    [
      externalLocations,
      dataLocations,
      externalAlerts,
      dataAlerts,
      externalRoutes,
      dataRoutes,
      externalStatistics,
      dataStatistics,
    ]
  );

  // Sidebar state management
  const {
    isOpen: sidebarOpen,
    activeTab,
    setIsOpen: setSidebarOpen,
    setActiveTab,
  } = useSidebarState();

  // Handle hydration and loading state
  if (!isHydrated || loading) {
    return <MapLoadingScreen language={language} />;
  }

  // Handle error state
  if (error) {
    return (
      <div className="fixed inset-0 flex items-center justify-center bg-gray-100">
        <div className="bg-white rounded-lg border p-8 text-center shadow-lg">
          <div className="text-red-500 mb-4">
            <svg
              className="w-12 h-12 mx-auto"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {language === "ar" ? "خطأ في تحميل البيانات" : "Error Loading Data"}
          </h3>
          <p className="text-gray-500 text-sm mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors"
          >
            {language === "ar" ? "إعادة المحاولة" : "Retry"}
          </button>
        </div>
      </div>
    );
  }

  const handleResetView = () => {
    console.log("Reset view clicked");
  };

  const handleFullscreen = () => {
    if (document.fullscreenElement) {
      document.exitFullscreen();
    } else {
      document.documentElement.requestFullscreen();
    }
  };

  return (
    <NoSSR fallback={<MapLoadingScreen language={language} />}>
      <div className="relative w-full h-full">
        {/* Google Map View */}
        <GoogleMapViewImproved
          locations={mapData.locations}
          center={initialCenter}
          zoom={initialZoom}
          onLocationClick={onLocationClick}
          language={language}
        />

        {/* Map Control Buttons */}
        <MapControlButtons
          sidebarOpen={showSidebar && sidebarOpen}
          onToggleSidebar={() => setSidebarOpen(!sidebarOpen)}
          onResetView={handleResetView}
          onFullscreen={handleFullscreen}
          language={language}
        />

        {/* Map Sidebar */}
        {showSidebar && (
          <MapSidebar
            isOpen={sidebarOpen}
            activeTab={activeTab}
            onTabChange={setActiveTab}
            onClose={() => setSidebarOpen(false)}
            alerts={mapData.alerts}
            routes={mapData.routes}
            statistics={mapData.statistics}
            language={language}
            onAlertClick={onAlertClick}
            onRouteSelect={onRouteSelect}
          />
        )}
      </div>
    </NoSSR>
  );
};

export default InteractiveMapContainer;
